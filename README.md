# Windsurf Python 认证客户端

这是一个Python实现的Windsurf认证客户端，支持多种登录方式，包括API密钥登录和浏览器OAuth登录。

## 🚀 功能特性

- ✅ **API密钥登录**: 直接使用Codeium API密钥登录（推荐）
- ✅ **浏览器OAuth登录**: 支持完整的OAuth 2.0认证流程
- ✅ **Token管理**: 自动保存、加载和验证访问token
- ✅ **跨平台支持**: 支持Windows、macOS和Linux
- ✅ **安全存储**: Token文件权限保护（非Windows系统）
- ✅ **简单易用**: 提供多个使用示例

## 📦 安装依赖

```bash
pip install -r requirements.txt
```

## 🔧 使用方法

### 🎯 快速开始（推荐）

```bash
# 运行快速开始脚本
python quick_start.py
```

### 方法1: API密钥登录（推荐）

```python
from windsurf_simple_auth import WindsurfSimpleAuth

# 创建认证实例
auth = WindsurfSimpleAuth()

# 使用API密钥登录
api_key = "your_codeium_api_key_here"
result = auth.login_with_api_key(api_key)

if result.get('success'):
    print("登录成功！")
    token = auth.get_valid_token()
    print(f"Access Token: {token}")
else:
    print(f"登录失败: {result.get('error_description')}")
```

### 方法2: 浏览器OAuth登录

```python
from windsurf_simple_auth import WindsurfSimpleAuth

# 创建认证实例
auth = WindsurfSimpleAuth()

# 浏览器登录
result = auth.login_browser()

if result.get('success'):
    print("登录成功！")
    token = auth.get_valid_token()
    print(f"Access Token: {token}")
```

### 检查现有Token

```python
# 检查是否已有有效token
existing_token = auth.get_valid_token()
if existing_token:
    print("已有有效token，无需重新登录")
else:
    print("需要登录")
```

## 🎯 运行演示

```bash
# 快速开始（推荐）
python quick_start.py

# 简化认证（多种选项）
python windsurf_simple_auth.py

# 完整OAuth认证（高级用户）
python windsurf_auth.py
```

## 📋 获取API密钥

1. 访问 [Codeium Profile](https://www.codeium.com/profile)
2. 登录您的Codeium账户
3. 在"API Key"部分复制您的密钥
4. 使用该密钥进行认证

## 📁 Token存储位置

Token会自动保存到以下位置：

- **Windows**: `%APPDATA%\.windsurf\auth_token.json`
- **macOS**: `~/Library/Application Support/.windsurf/auth_token.json`
- **Linux**: `~/.windsurf/auth_token.json`

## 🔐 认证流程详解

1. **生成认证URL**: 包含PKCE挑战和状态参数
2. **启动本地服务器**: 监听OAuth回调（端口8080）
3. **打开浏览器**: 自动跳转到Windsurf登录页面
4. **用户认证**: 用户在浏览器中完成登录
5. **处理回调**: 本地服务器接收授权码
6. **交换Token**: 使用授权码获取访问token
7. **保存Token**: 将token安全保存到本地文件

## 🛠️ API参考

### WindsurfAuth类

#### 方法

- `__init__(data_dir=None)`: 初始化认证客户端
- `login()`: 执行完整登录流程
- `load_token()`: 从本地文件加载token
- `get_valid_token()`: 获取有效的访问token
- `is_token_valid(token_data=None)`: 检查token是否有效
- `logout()`: 登出并删除本地token
- `generate_auth_url()`: 生成OAuth认证URL

#### 配置参数

```python
# OAuth配置（可根据需要调整）
AUTH_BASE_URL = "https://codeium.com/profile"
TOKEN_URL = "https://api.codeium.com/oauth/token"
CLIENT_ID = "windsurf-desktop"
REDIRECT_URI = "http://localhost:8080/callback"
SCOPE = "openid profile email"
```

## 🔧 自定义配置

如果需要自定义配置，可以修改`WindsurfAuth`类中的常量：

```python
class WindsurfAuth:
    # 自定义OAuth端点
    AUTH_BASE_URL = "https://your-auth-server.com/oauth/authorize"
    TOKEN_URL = "https://your-auth-server.com/oauth/token"
    CLIENT_ID = "your-client-id"
    
    # 自定义回调端口
    REDIRECT_URI = "http://localhost:9000/callback"
```

## ⚠️ 注意事项

1. **端口占用**: 默认使用8080端口作为回调服务器，确保端口未被占用
2. **防火墙**: 确保防火墙允许本地8080端口的连接
3. **浏览器**: 需要系统默认浏览器支持
4. **网络**: 需要能够访问Codeium的认证服务器

## 🐛 故障排除

### 常见问题

1. **端口被占用**
   ```
   解决方案: 修改REDIRECT_URI中的端口号
   ```

2. **浏览器未打开**
   ```
   解决方案: 手动复制认证URL到浏览器
   ```

3. **Token过期**
   ```
   解决方案: 重新执行login()方法
   ```

4. **网络连接问题**
   ```
   解决方案: 检查网络连接和防火墙设置
   ```

## 📄 许可证

本项目仅供学习和研究使用。请遵守Windsurf/Codeium的服务条款。

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目！
