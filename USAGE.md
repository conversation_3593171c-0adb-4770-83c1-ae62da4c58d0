# Windsurf 全自动认证使用指南

## 🎯 推荐使用方式

### 方式1: 完全自动认证（最简单）

```bash
python windsurf_full_auto.py
```

**特点：**
- ✅ 一键登录，完全自动化
- ✅ 自动打开浏览器认证页面
- ✅ 图形化界面，操作简单
- ✅ 自动保存token到本地

**使用步骤：**
1. 运行脚本
2. 浏览器自动打开认证页面
3. 点击链接打开Codeium Profile
4. 复制API密钥并粘贴
5. 点击完成登录
6. 自动保存token，完成！

### 方式2: 快速开始（推荐新手）

```bash
python quick_start.py
```

**特点：**
- ✅ 命令行交互式
- ✅ 步骤清晰明了
- ✅ 适合新手使用

### 方式3: Playwright自动化（高级用户）

```bash
# 需要先安装Playwright
pip install playwright
playwright install

python windsurf_playwright_auth.py
```

**特点：**
- ✅ 完全自动化浏览器操作
- ✅ 支持用户名密码登录
- ✅ 自动提取API密钥

### 方式4: 设备码认证（GitHub用户）

```bash
python windsurf_auto_auth.py
```

**特点：**
- ✅ 基于GitHub OAuth
- ✅ 设备码流程
- ✅ 适合GitHub用户

## 📋 获取API密钥步骤

1. 访问 [Codeium Profile](https://www.codeium.com/profile)
2. 登录您的Codeium账户
3. 在"API Key"部分复制您的密钥
4. 使用任意方式进行认证

## 📁 Token存储位置

- **Windows**: `%APPDATA%\.windsurf\auth_token.json`
- **macOS**: `~/Library/Application Support/.windsurf/auth_token.json`
- **Linux**: `~/.windsurf/auth_token.json`

## 🔧 编程使用示例

```python
from windsurf_full_auto import WindsurfFullAuto

# 创建认证实例
auth = WindsurfFullAuto()

# 检查现有token
token = auth.get_valid_token()
if token:
    print(f"已有token: {token}")
else:
    # 执行一键登录
    result = auth.one_click_login()
    if result.get('success'):
        token = auth.get_valid_token()
        print(f"登录成功: {token}")
```

## 🚀 各种认证方式对比

| 方式 | 难度 | 自动化程度 | 适用场景 |
|------|------|------------|----------|
| windsurf_full_auto.py | ⭐ | 🤖🤖🤖🤖 | 推荐所有用户 |
| quick_start.py | ⭐⭐ | 🤖🤖🤖 | 新手用户 |
| windsurf_playwright_auth.py | ⭐⭐⭐ | 🤖🤖🤖🤖🤖 | 高级用户 |
| windsurf_auto_auth.py | ⭐⭐⭐ | 🤖🤖🤖🤖 | GitHub用户 |
| windsurf_simple_auth.py | ⭐⭐ | 🤖🤖 | 手动输入 |

## ⚠️ 注意事项

1. **端口占用**: 确保8080端口未被占用
2. **防火墙**: 允许本地8080端口连接
3. **浏览器**: 需要默认浏览器支持
4. **网络**: 需要访问Codeium服务器

## 🐛 故障排除

### 常见问题

1. **端口被占用**
   ```
   解决方案: 关闭占用8080端口的程序，或修改代码中的端口号
   ```

2. **浏览器未打开**
   ```
   解决方案: 手动访问 http://localhost:8080/auth
   ```

3. **Token无效**
   ```
   解决方案: 重新获取API密钥并登录
   ```

4. **网络连接问题**
   ```
   解决方案: 检查网络连接和防火墙设置
   ```

## 🎉 成功标志

当看到以下信息时，表示登录成功：

```
✅ 登录成功！Token已保存到本地。
🎉 一键登录成功！
🔑 Token: abcdefghij1234567890...
📁 Token文件: C:\Users\<USER>\AppData\Roaming\.windsurf\auth_token.json
```

## 🔄 重新登录

如果需要重新登录：

```python
auth.logout()  # 删除本地token
auth.one_click_login()  # 重新登录
```

## 📞 技术支持

如果遇到问题，请检查：
1. Python版本 >= 3.7
2. 网络连接正常
3. 防火墙设置
4. Codeium账户状态

---

**推荐使用 `windsurf_full_auto.py` 获得最佳体验！**
