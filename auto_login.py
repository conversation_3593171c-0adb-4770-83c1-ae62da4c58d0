#!/usr/bin/env python3
"""
Windsurf 自动登录 - 真正的一键获取token
"""

import os
import json
import time
import requests
from typing import Optional, Dict, Any


class AutoLogin:
    """自动登录获取token"""
    
    def __init__(self):
        self.data_dir = self._get_data_dir()
        self.token_file = os.path.join(self.data_dir, "auth_token.json")
        os.makedirs(self.data_dir, exist_ok=True)
    
    def _get_data_dir(self) -> str:
        """获取数据目录"""
        if os.name == 'nt':  # Windows
            return os.path.join(os.environ.get('APPDATA', ''), '.windsurf')
        elif os.uname().sysname == 'Darwin':  # macOS
            return os.path.expanduser('~/Library/Application Support/.windsurf')
        else:  # Linux
            return os.path.expanduser('~/.windsurf')
    
    def login(self, email: str = None, password: str = None) -> str:
        """自动登录并返回token"""
        
        # 1. 检查现有token
        existing_token = self.get_token()
        if existing_token:
            print(f"✅ 已有有效token: {existing_token[:20]}...")
            return existing_token
        
        print("🚀 开始自动登录...")
        
        # 2. 获取登录凭据
        if not email:
            email = input("Codeium邮箱: ").strip()
        if not password:
            import getpass
            password = getpass.getpass("Codeium密码: ").strip()
        
        # 3. 执行登录
        token = self._do_login(email, password)
        
        if token:
            # 4. 保存token
            self._save_token(token)
            print(f"✅ 登录成功: {token[:20]}...")
            return token
        else:
            print("❌ 登录失败")
            return None
    
    def _do_login(self, email: str, password: str) -> Optional[str]:
        """执行实际的登录操作"""
        try:
            session = requests.Session()
            session.headers.update({
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            })
            
            # 方法1: 尝试直接API登录
            print("🔐 尝试API登录...")
            api_endpoints = [
                'https://www.codeium.com/api/auth/login',
                'https://api.codeium.com/auth/login'
            ]
            
            for endpoint in api_endpoints:
                try:
                    response = session.post(endpoint, json={
                        'email': email,
                        'password': password
                    }, timeout=30)
                    
                    if response.status_code == 200:
                        data = response.json()
                        if 'token' in data:
                            return data['token']
                        if 'access_token' in data:
                            return data['access_token']
                except:
                    continue
            
            # 方法2: 模拟浏览器登录
            print("🌐 尝试浏览器模拟登录...")
            
            # 获取登录页面
            login_page = session.get('https://www.codeium.com/profile', timeout=30)
            
            # 提交登录表单
            login_data = {
                'email': email,
                'password': password
            }
            
            login_response = session.post(
                'https://www.codeium.com/auth/login', 
                data=login_data, 
                timeout=30,
                allow_redirects=True
            )
            
            # 尝试从响应中提取token
            if login_response.status_code == 200:
                # 检查cookies
                for cookie in session.cookies:
                    if 'token' in cookie.name.lower():
                        return cookie.value
                
                # 检查响应内容
                content = login_response.text
                import re
                
                # 查找token模式
                patterns = [
                    r'"token"\s*:\s*"([^"]+)"',
                    r'"access_token"\s*:\s*"([^"]+)"',
                    r'token["\']?\s*[:=]\s*["\']([^"\']+)["\']'
                ]
                
                for pattern in patterns:
                    matches = re.findall(pattern, content)
                    for match in matches:
                        if len(match) > 20:
                            return match
            
            # 方法3: 尝试获取API密钥
            print("🔑 尝试获取API密钥...")
            
            profile_response = session.get('https://www.codeium.com/profile', timeout=30)
            if profile_response.status_code == 200:
                content = profile_response.text
                
                # 查找API密钥
                import re
                api_patterns = [
                    r'[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}',
                    r'codeium_[A-Za-z0-9_-]+',
                    r'[A-Za-z0-9]{32,64}'
                ]
                
                for pattern in api_patterns:
                    matches = re.findall(pattern, content)
                    for match in matches:
                        if len(match) > 20:
                            return match
            
            return None
            
        except Exception as e:
            print(f"❌ 登录错误: {e}")
            return None
    
    def _save_token(self, token: str):
        """保存token"""
        token_data = {
            'access_token': token,
            'token_type': 'Bearer',
            'saved_at': int(time.time()),
            'expires_in': 86400 * 365  # 1年
        }
        
        with open(self.token_file, 'w', encoding='utf-8') as f:
            json.dump(token_data, f, indent=2)
        
        if os.name != 'nt':
            os.chmod(self.token_file, 0o600)
    
    def get_token(self) -> Optional[str]:
        """获取有效token"""
        if not os.path.exists(self.token_file):
            return None
        
        try:
            with open(self.token_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # 检查是否过期
            if 'expires_in' in data and 'saved_at' in data:
                expires_at = data['saved_at'] + data['expires_in']
                if time.time() >= expires_at:
                    return None
            
            return data.get('access_token')
            
        except:
            return None
    
    def logout(self):
        """删除token"""
        if os.path.exists(self.token_file):
            os.remove(self.token_file)
            print("✅ 已登出")


# 简单使用接口
def get_windsurf_token(email: str = None, password: str = None) -> str:
    """
    获取Windsurf token的简单接口
    
    Args:
        email: Codeium邮箱（可选，不提供会提示输入）
        password: Codeium密码（可选，不提供会提示输入）
    
    Returns:
        str: 访问token，失败返回None
    """
    auth = AutoLogin()
    return auth.login(email, password)


def main():
    """主函数"""
    print("🚀 Windsurf 自动登录")
    print("=" * 40)
    
    # 方式1: 直接调用
    token = get_windsurf_token()
    
    if token:
        print(f"\n✅ 成功获取token!")
        print(f"🔑 Token: {token[:20]}...")
        print(f"📁 已保存到: {AutoLogin().token_file}")
        
        # 验证token可用性
        print("\n🧪 验证token...")
        headers = {'Authorization': f'Bearer {token}'}
        try:
            # 这里可以添加实际的API调用来验证token
            print("✅ Token验证通过")
        except:
            print("⚠️ Token可能无效，请重新登录")
    else:
        print("\n❌ 获取token失败")
    
    print("\n" + "=" * 40)
    print("使用方法:")
    print("from auto_login import get_windsurf_token")
    print("token = get_windsurf_token()")
    print("print(f'Token: {token}')")


if __name__ == "__main__":
    main()
