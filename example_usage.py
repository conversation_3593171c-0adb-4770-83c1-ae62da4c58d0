#!/usr/bin/env python3
"""
Windsurf认证使用示例
演示如何使用WindsurfAuth类进行登录和API调用
"""

import requests
from windsurf_auth import WindsurfAuth


class WindsurfClient:
    """Windsurf API客户端"""
    
    def __init__(self, auth: WindsurfAuth):
        self.auth = auth
        self.base_url = "https://api.codeium.com"
    
    def _get_headers(self) -> dict:
        """获取API请求头"""
        token = self.auth.get_valid_token()
        if not token:
            raise Exception("No valid token available. Please login first.")
        
        return {
            'Authorization': f'Bearer {token}',
            'Content-Type': 'application/json',
            'User-Agent': 'Windsurf-Python-Client/1.0'
        }
    
    def get_user_info(self) -> dict:
        """获取用户信息"""
        headers = self._get_headers()
        response = requests.get(f"{self.base_url}/user", headers=headers)
        response.raise_for_status()
        return response.json()
    
    def get_api_key(self) -> str:
        """获取API密钥"""
        headers = self._get_headers()
        response = requests.get(f"{self.base_url}/api_key", headers=headers)
        response.raise_for_status()
        return response.json().get('api_key', '')


def demo_login_flow():
    """演示完整的登录流程"""
    print("=== Windsurf 登录演示 ===\n")
    
    # 创建认证实例
    auth = WindsurfAuth()
    
    # 检查现有token
    print("1. 检查现有token...")
    existing_token = auth.get_valid_token()
    if existing_token:
        print(f"✅ 找到有效token: {existing_token[:20]}...")
        choice = input("是否重新登录？(y/N): ").strip().lower()
        if choice != 'y':
            print("使用现有token。")
            return auth
    else:
        print("❌ 未找到有效token，需要登录。")
    
    # 执行登录
    print("\n2. 开始登录流程...")
    result = auth.login()
    
    if result.get('success'):
        print("✅ 登录成功！")
        token_data = result.get('token_data', {})
        print(f"   Access Token: {token_data.get('access_token', '')[:20]}...")
        print(f"   Token Type: {token_data.get('token_type', 'N/A')}")
        print(f"   Expires In: {token_data.get('expires_in', 'N/A')} seconds")
        print(f"   Scope: {token_data.get('scope', 'N/A')}")
        return auth
    else:
        print(f"❌ 登录失败: {result.get('error_description', 'Unknown error')}")
        return None


def demo_api_calls(auth: WindsurfAuth):
    """演示API调用"""
    print("\n=== API调用演示 ===\n")
    
    client = WindsurfClient(auth)
    
    try:
        # 获取用户信息
        print("1. 获取用户信息...")
        user_info = client.get_user_info()
        print(f"   用户ID: {user_info.get('id', 'N/A')}")
        print(f"   用户名: {user_info.get('username', 'N/A')}")
        print(f"   邮箱: {user_info.get('email', 'N/A')}")
        
        # 获取API密钥
        print("\n2. 获取API密钥...")
        api_key = client.get_api_key()
        print(f"   API Key: {api_key[:20] if api_key else 'N/A'}...")
        
    except Exception as e:
        print(f"❌ API调用失败: {str(e)}")


def demo_token_management():
    """演示token管理功能"""
    print("\n=== Token管理演示 ===\n")
    
    auth = WindsurfAuth()
    
    # 显示token信息
    print("1. Token信息:")
    token_data = auth.load_token()
    if token_data:
        print(f"   Token文件: {auth.token_file}")
        print(f"   保存时间: {token_data.get('saved_at', 'N/A')}")
        print(f"   是否有效: {'是' if auth.is_token_valid(token_data) else '否'}")
    else:
        print("   未找到token文件")
    
    # 登出选项
    if token_data:
        print("\n2. 登出选项:")
        choice = input("是否要登出并删除本地token？(y/N): ").strip().lower()
        if choice == 'y':
            auth.logout()


def main():
    """主函数"""
    print("🚀 Windsurf Python认证客户端\n")
    
    while True:
        print("\n请选择操作:")
        print("1. 登录演示")
        print("2. API调用演示")
        print("3. Token管理")
        print("4. 退出")
        
        choice = input("\n请输入选项 (1-4): ").strip()
        
        if choice == '1':
            auth = demo_login_flow()
            if auth:
                print(f"\n✅ 认证完成！Token已保存到: {auth.token_file}")
        
        elif choice == '2':
            auth = WindsurfAuth()
            if auth.get_valid_token():
                demo_api_calls(auth)
            else:
                print("❌ 未找到有效token，请先登录。")
        
        elif choice == '3':
            demo_token_management()
        
        elif choice == '4':
            print("👋 再见！")
            break
        
        else:
            print("❌ 无效选项，请重试。")


if __name__ == "__main__":
    main()
