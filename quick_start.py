#!/usr/bin/env python3
"""
Windsurf 快速开始示例
"""

from windsurf_simple_auth import WindsurfSimpleAuth


def main():
    print("🚀 Windsurf 快速认证\n")
    
    # 创建认证实例
    auth = WindsurfSimpleAuth()
    
    # 检查现有token
    token = auth.get_valid_token()
    if token:
        print(f"✅ 已有有效token: {token[:20]}...")
        print(f"📁 Token文件: {auth.token_file}")
        return token
    
    print("❌ 未找到有效token")
    print("\n获取Codeium API密钥的步骤:")
    print("1. 访问 https://www.codeium.com/profile")
    print("2. 登录您的账户")
    print("3. 在'API Key'部分复制您的密钥")
    
    api_key = input("\n请输入您的Codeium API密钥: ").strip()
    
    if not api_key:
        print("❌ 未输入API密钥")
        return None
    
    # 使用API密钥登录
    result = auth.login_with_api_key(api_key)
    
    if result.get('success'):
        token = auth.get_valid_token()
        print(f"\n✅ 登录成功！")
        print(f"🔑 Token: {token[:20] if token else 'None'}...")
        print(f"📁 Token文件: {auth.token_file}")
        return token
    else:
        print(f"\n❌ 登录失败: {result.get('error_description')}")
        return None


if __name__ == "__main__":
    main()
