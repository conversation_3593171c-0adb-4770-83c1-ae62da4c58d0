#!/usr/bin/env python3
"""
Windsurf认证简单使用示例
"""

from windsurf_auth import WindsurfAuth


def main():
    # 创建认证实例
    auth = WindsurfAuth()
    
    # 检查现有token
    token = auth.get_valid_token()
    if token:
        print(f"已有有效token: {token[:20]}...")
        return token
    
    # 执行登录
    result = auth.login()
    
    if result.get('success'):
        token = auth.get_valid_token()
        print(f"登录成功，token: {token[:20] if token else 'None'}...")
        return token
    else:
        print(f"登录失败: {result.get('error_description')}")
        return None


if __name__ == "__main__":
    main()
