#!/usr/bin/env python3
"""
Windsurf认证简单使用示例
"""

from windsurf_auth import WindsurfAuth


def test_auth_url():
    """测试认证URL生成"""
    print("=== 测试认证URL生成 ===")
    auth = WindsurfAuth()
    auth_url, state, code_verifier = auth.generate_auth_url()
    print(f"认证URL: {auth_url}")
    print(f"状态参数: {state}")
    print(f"代码验证器: {code_verifier[:20]}...")
    print()


def main():
    print("🚀 Windsurf认证测试\n")

    # 测试URL生成
    test_auth_url()

    # 创建认证实例
    auth = WindsurfAuth()
    print(f"📁 Token文件位置: {auth.token_file}")

    # 检查现有token
    token = auth.get_valid_token()
    if token:
        print(f"✅ 已有有效token: {token[:20]}...")
        choice = input("是否重新登录？(y/N): ").strip().lower()
        if choice != 'y':
            return token
    else:
        print("❌ 未找到有效token，开始登录流程...")

    # 执行登录
    result = auth.login()

    if result.get('success'):
        token = auth.get_valid_token()
        print(f"🎉 登录成功！")
        print(f"Token: {token[:20] if token else 'None'}...")
        print(f"Token文件: {auth.token_file}")
        return token
    else:
        print(f"💥 登录失败: {result.get('error_description')}")
        return None


if __name__ == "__main__":
    main()
