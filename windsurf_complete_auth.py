#!/usr/bin/env python3
"""
Windsurf 完整官方认证实现
完全按照Windsurf官方的OAuth 2.0 + PKCE流程实现
包含完整的错误处理、token管理、API调用等功能
"""

import os
import json
import time
import uuid
import hashlib
import base64
import secrets
import webbrowser
import urllib.parse
from http.server import HTTPServer, BaseHTTPRequestHandler
from threading import Thread, Event
import requests
from typing import Optional, Dict, Any, List


class WindsurfCompleteAuth:
    """Windsurf完整官方认证客户端"""
    
    # 官方Windsurf/Codeium OAuth配置
    AUTH_BASE_URL = "https://www.codeium.com/profile"
    TOKEN_URL = "https://www.codeium.com/api/auth/oauth/token"
    REFRESH_URL = "https://www.codeium.com/api/auth/oauth/refresh"
    REVOKE_URL = "https://www.codeium.com/api/auth/oauth/revoke"
    API_BASE_URL = "https://server.codeium.com"
    USER_INFO_URL = "https://www.codeium.com/api/user/info"
    
    # 官方OAuth客户端配置
    CLIENT_ID = "windsurf-desktop"
    CLIENT_SECRET = None  # 公共客户端，无需密钥
    REDIRECT_URI = "http://localhost:8080/callback"
    SCOPE = "openid profile email"
    
    # 回调服务器配置
    CALLBACK_HOST = "localhost"
    CALLBACK_PORT = 8080
    CALLBACK_TIMEOUT = 300  # 5分钟
    
    def __init__(self, data_dir: Optional[str] = None, port: int = None):
        """
        初始化认证客户端
        
        Args:
            data_dir: 用户数据目录，默认为系统标准位置
            port: 回调服务器端口，默认8080
        """
        self.data_dir = data_dir or self._get_default_data_dir()
        self.token_file = os.path.join(self.data_dir, "auth_token.json")
        self.session_file = os.path.join(self.data_dir, "auth_session.json")
        
        if port:
            self.CALLBACK_PORT = port
            self.REDIRECT_URI = f"http://{self.CALLBACK_HOST}:{port}/callback"
        
        self.callback_server = None
        self.auth_result = {}
        self.server_ready = Event()
        self.session = requests.Session()
        
        # 确保数据目录存在
        os.makedirs(self.data_dir, exist_ok=True)
        
        # 设置会话头
        self.session.headers.update({
            'User-Agent': 'Windsurf-Desktop/1.0.0 (OAuth2-Client)',
            'Accept': 'application/json',
            'Content-Type': 'application/json'
        })
    
    def _get_default_data_dir(self) -> str:
        """获取默认的用户数据目录"""
        if os.name == 'nt':  # Windows
            return os.path.join(os.environ.get('APPDATA', ''), '.windsurf')
        elif os.uname().sysname == 'Darwin':  # macOS
            return os.path.expanduser('~/Library/Application Support/.windsurf')
        else:  # Linux
            return os.path.expanduser('~/.windsurf')
    
    def _generate_pkce_pair(self) -> tuple[str, str]:
        """
        生成PKCE代码验证器和挑战
        按照RFC 7636标准实现
        """
        # 生成43-128字符的代码验证器
        code_verifier = base64.urlsafe_b64encode(secrets.token_bytes(32)).decode('utf-8').rstrip('=')
        
        # 生成代码挑战 (SHA256)
        code_challenge = base64.urlsafe_b64encode(
            hashlib.sha256(code_verifier.encode('utf-8')).digest()
        ).decode('utf-8').rstrip('=')
        
        return code_verifier, code_challenge
    
    def _generate_state(self) -> str:
        """生成状态参数防止CSRF攻击"""
        return str(uuid.uuid4())
    
    def generate_auth_url(self) -> tuple[str, str, str]:
        """
        生成OAuth 2.0认证URL
        
        Returns:
            tuple: (auth_url, state, code_verifier)
        """
        state = self._generate_state()
        code_verifier, code_challenge = self._generate_pkce_pair()
        
        # OAuth 2.0 + PKCE参数
        params = {
            'response_type': 'code',
            'client_id': self.CLIENT_ID,
            'redirect_uri': self.REDIRECT_URI,
            'scope': self.SCOPE,
            'state': state,
            'code_challenge': code_challenge,
            'code_challenge_method': 'S256',
            'prompt': 'consent',  # 强制显示同意页面
            'access_type': 'offline'  # 请求刷新令牌
        }
        
        auth_url = f"{self.AUTH_BASE_URL}?{urllib.parse.urlencode(params)}"
        
        # 保存会话信息
        self._save_session({
            'state': state,
            'code_verifier': code_verifier,
            'timestamp': int(time.time())
        })
        
        return auth_url, state, code_verifier
    
    def login(self) -> Dict[str, Any]:
        """
        执行完整的OAuth登录流程
        
        Returns:
            Dict: 登录结果
        """
        print("🚀 开始Windsurf官方OAuth认证...")
        
        # 检查现有token
        existing_token = self.get_valid_token()
        if existing_token:
            print(f"✅ 发现有效token: {existing_token[:20]}...")
            user_info = self.get_user_info()
            if user_info:
                print(f"👤 用户: {user_info.get('email', 'Unknown')}")
            return {
                'success': True, 
                'token': existing_token, 
                'from_cache': True,
                'user_info': user_info
            }
        
        # 生成认证URL
        auth_url, state, code_verifier = self.generate_auth_url()
        print(f"🔗 认证URL: {auth_url}")
        print(f"🔐 状态参数: {state}")
        print(f"🔑 PKCE验证器: {code_verifier[:20]}...")
        
        # 启动回调服务器
        print(f"🌐 启动回调服务器 {self.CALLBACK_HOST}:{self.CALLBACK_PORT}...")
        server_thread = Thread(
            target=self._start_callback_server, 
            args=(state, code_verifier), 
            daemon=True
        )
        server_thread.start()
        
        if not self.server_ready.wait(timeout=10):
            return {
                'success': False, 
                'error': 'server_start_failed', 
                'error_description': '回调服务器启动失败'
            }
        
        print("✅ 回调服务器已启动")
        
        # 打开浏览器
        print("🔗 打开浏览器进行认证...")
        try:
            webbrowser.open(auth_url)
            print("✅ 浏览器已打开")
        except Exception as e:
            print(f"❌ 浏览器打开失败: {e}")
            print(f"请手动访问: {auth_url}")
        
        # 等待认证完成
        print(f"⏳ 等待认证完成 (超时: {self.CALLBACK_TIMEOUT}秒)...")
        server_thread.join(timeout=self.CALLBACK_TIMEOUT)
        
        if not self.auth_result:
            return {
                'success': False, 
                'error': 'timeout', 
                'error_description': f'认证超时 ({self.CALLBACK_TIMEOUT}秒)'
            }
        
        # 处理认证结果
        if self.auth_result.get('success'):
            token_data = self.auth_result['token_data']
            self._save_token(token_data)
            
            token = self.get_valid_token()
            user_info = self.get_user_info()
            
            print("✅ 登录成功！Token已保存到本地。")
            if user_info:
                print(f"👤 欢迎: {user_info.get('email', 'Unknown')}")
            
            return {
                'success': True, 
                'token': token, 
                'from_cache': False,
                'user_info': user_info,
                'token_data': token_data
            }
        else:
            error_desc = self.auth_result.get('error_description', 'Unknown error')
            print(f"❌ 登录失败: {error_desc}")
            return self.auth_result
    
    def _start_callback_server(self, expected_state: str, code_verifier: str):
        """启动OAuth回调服务器"""
        class OAuthCallbackHandler(BaseHTTPRequestHandler):
            def __init__(self, auth_instance, expected_state, code_verifier, *args, **kwargs):
                self.auth_instance = auth_instance
                self.expected_state = expected_state
                self.code_verifier = code_verifier
                super().__init__(*args, **kwargs)
            
            def log_message(self, format, *args):
                # 禁用默认日志输出
                pass
            
            def do_GET(self):
                try:
                    self._handle_oauth_callback()
                except Exception as e:
                    print(f"❌ 回调处理错误: {e}")
                    self.auth_instance.auth_result = {
                        'success': False,
                        'error': 'callback_error',
                        'error_description': str(e)
                    }
                    self._send_error_response(str(e))
            
            def _handle_oauth_callback(self):
                # 解析回调URL
                parsed_url = urllib.parse.urlparse(self.path)
                query_params = urllib.parse.parse_qs(parsed_url.query)
                
                print(f"🔍 收到OAuth回调: {self.path}")
                print(f"🔍 查询参数: {list(query_params.keys())}")
                
                # 检查OAuth错误
                if 'error' in query_params:
                    error = query_params['error'][0]
                    error_description = query_params.get('error_description', ['Unknown OAuth error'])[0]
                    error_uri = query_params.get('error_uri', [None])[0]
                    
                    print(f"❌ OAuth错误: {error}")
                    print(f"❌ 错误描述: {error_description}")
                    
                    self.auth_instance.auth_result = {
                        'success': False,
                        'error': error,
                        'error_description': error_description,
                        'error_uri': error_uri
                    }
                    self._send_error_response(f"OAuth错误: {error_description}")
                    return
                
                # 验证状态参数
                received_state = query_params.get('state', [None])[0]
                if received_state != self.expected_state:
                    print(f"❌ 状态参数不匹配: 期望 {self.expected_state}, 收到 {received_state}")
                    self.auth_instance.auth_result = {
                        'success': False,
                        'error': 'invalid_state',
                        'error_description': '状态参数验证失败，可能存在CSRF攻击'
                    }
                    self._send_error_response("状态验证失败")
                    return
                
                # 获取授权码
                authorization_code = query_params.get('code', [None])[0]
                if not authorization_code:
                    print("❌ 未收到授权码")
                    self.auth_instance.auth_result = {
                        'success': False,
                        'error': 'no_authorization_code',
                        'error_description': '授权服务器未返回授权码'
                    }
                    self._send_error_response("未收到授权码")
                    return
                
                print(f"🔑 收到授权码: {authorization_code[:20]}...")
                
                # 交换访问令牌
                try:
                    token_data = self.auth_instance._exchange_authorization_code(
                        authorization_code, 
                        self.code_verifier
                    )
                    
                    print("✅ 成功获取访问令牌")
                    self.auth_instance.auth_result = {
                        'success': True,
                        'token_data': token_data
                    }
                    self._send_success_response()
                    
                except Exception as e:
                    print(f"❌ 令牌交换失败: {e}")
                    self.auth_instance.auth_result = {
                        'success': False,
                        'error': 'token_exchange_failed',
                        'error_description': f'令牌交换失败: {str(e)}'
                    }
                    self._send_error_response(f"令牌交换失败: {str(e)}")
            
            def _send_success_response(self):
                """发送成功响应页面"""
                html = """<!DOCTYPE html>
                <html>
                <head>
                    <title>Windsurf 认证成功</title>
                    <meta charset="utf-8">
                    <style>
                        body { 
                            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                            margin: 0; padding: 50px; text-align: center; color: white;
                        }
                        .container { 
                            background: rgba(255,255,255,0.95); 
                            padding: 40px; border-radius: 15px; 
                            max-width: 500px; margin: 0 auto;
                            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
                            color: #333;
                        }
                        .success-icon { font-size: 64px; margin-bottom: 20px; }
                        .title { font-size: 24px; font-weight: bold; margin-bottom: 15px; color: #28a745; }
                        .message { font-size: 16px; line-height: 1.5; margin-bottom: 20px; }
                        .footer { font-size: 14px; color: #666; }
                    </style>
                </head>
                <body>
                    <div class="container">
                        <div class="success-icon">✅</div>
                        <div class="title">认证成功！</div>
                        <div class="message">
                            您已成功登录Windsurf。<br>
                            访问令牌已安全保存到本地。<br>
                            您现在可以关闭此页面。
                        </div>
                        <div class="footer">Windsurf Desktop Client</div>
                    </div>
                    <script>
                        setTimeout(() => {
                            try { window.close(); } catch(e) {}
                        }, 3000);
                    </script>
                </body>
                </html>"""
                
                self.send_response(200)
                self.send_header('Content-Type', 'text/html; charset=utf-8')
                self.send_header('Cache-Control', 'no-cache, no-store, must-revalidate')
                self.end_headers()
                self.wfile.write(html.encode('utf-8'))
            
            def _send_error_response(self, error_message: str):
                """发送错误响应页面"""
                html = f"""<!DOCTYPE html>
                <html>
                <head>
                    <title>Windsurf 认证失败</title>
                    <meta charset="utf-8">
                    <style>
                        body {{ 
                            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
                            margin: 0; padding: 50px; text-align: center; color: white;
                        }}
                        .container {{ 
                            background: rgba(255,255,255,0.95); 
                            padding: 40px; border-radius: 15px; 
                            max-width: 500px; margin: 0 auto;
                            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
                            color: #333;
                        }}
                        .error-icon {{ font-size: 64px; margin-bottom: 20px; }}
                        .title {{ font-size: 24px; font-weight: bold; margin-bottom: 15px; color: #dc3545; }}
                        .message {{ font-size: 16px; line-height: 1.5; margin-bottom: 20px; }}
                        .footer {{ font-size: 14px; color: #666; }}
                        .retry-btn {{ 
                            background: #007bff; color: white; padding: 10px 20px; 
                            border: none; border-radius: 5px; cursor: pointer; 
                            font-size: 14px; margin-top: 15px;
                        }}
                    </style>
                </head>
                <body>
                    <div class="container">
                        <div class="error-icon">❌</div>
                        <div class="title">认证失败</div>
                        <div class="message">
                            {error_message}<br><br>
                            请重试或检查网络连接。
                        </div>
                        <button class="retry-btn" onclick="window.close()">关闭页面</button>
                        <div class="footer">Windsurf Desktop Client</div>
                    </div>
                </body>
                </html>"""
                
                self.send_response(400)
                self.send_header('Content-Type', 'text/html; charset=utf-8')
                self.send_header('Cache-Control', 'no-cache, no-store, must-revalidate')
                self.end_headers()
                self.wfile.write(html.encode('utf-8'))
        
        # 创建服务器处理器
        def create_handler(*args, **kwargs):
            return OAuthCallbackHandler(self, expected_state, code_verifier, *args, **kwargs)
        
        try:
            # 创建HTTP服务器
            self.callback_server = HTTPServer((self.CALLBACK_HOST, self.CALLBACK_PORT), create_handler)
            self.server_ready.set()
            
            print(f"🌐 OAuth回调服务器已启动: http://{self.CALLBACK_HOST}:{self.CALLBACK_PORT}")
            
            # 处理单个请求后关闭
            self.callback_server.handle_request()
            self.callback_server.server_close()
            
            print("🔒 回调服务器已关闭")
            
        except OSError as e:
            if e.errno == 10048:  # Windows: Address already in use
                error_msg = f"端口 {self.CALLBACK_PORT} 已被占用，请关闭占用该端口的程序或使用其他端口"
            else:
                error_msg = f"服务器启动失败: {e}"
            
            print(f"❌ {error_msg}")
            self.auth_result = {
                'success': False,
                'error': 'server_start_failed',
                'error_description': error_msg
            }
        except Exception as e:
            print(f"❌ 回调服务器异常: {e}")
            self.auth_result = {
                'success': False,
                'error': 'server_error',
                'error_description': str(e)
            }

    def _exchange_authorization_code(self, code: str, code_verifier: str) -> Dict[str, Any]:
        """
        使用授权码交换访问令牌
        按照OAuth 2.0 + PKCE标准实现

        Args:
            code: 授权码
            code_verifier: PKCE代码验证器

        Returns:
            Dict: 令牌数据
        """
        token_request_data = {
            'grant_type': 'authorization_code',
            'client_id': self.CLIENT_ID,
            'code': code,
            'redirect_uri': self.REDIRECT_URI,
            'code_verifier': code_verifier,
        }

        # 如果有客户端密钥，添加到请求中
        if self.CLIENT_SECRET:
            token_request_data['client_secret'] = self.CLIENT_SECRET

        headers = {
            'Content-Type': 'application/x-www-form-urlencoded',
            'Accept': 'application/json',
            'User-Agent': 'Windsurf-Desktop/1.0.0'
        }

        print(f"🔄 向令牌端点交换访问令牌: {self.TOKEN_URL}")

        try:
            response = self.session.post(
                self.TOKEN_URL,
                data=token_request_data,
                headers=headers,
                timeout=30
            )

            print(f"📡 令牌响应状态: {response.status_code}")

            if response.status_code == 200:
                token_data = response.json()

                # 验证必需的令牌字段
                required_fields = ['access_token', 'token_type']
                missing_fields = [field for field in required_fields if field not in token_data]

                if missing_fields:
                    raise Exception(f"令牌响应缺少必需字段: {missing_fields}")

                # 添加时间戳
                token_data['obtained_at'] = int(time.time())

                print(f"✅ 成功获取访问令牌")
                print(f"🔑 令牌类型: {token_data.get('token_type', 'Unknown')}")
                print(f"⏰ 过期时间: {token_data.get('expires_in', 'Unknown')} 秒")

                if 'refresh_token' in token_data:
                    print("🔄 包含刷新令牌")

                return token_data

            elif response.status_code == 400:
                # OAuth错误响应
                try:
                    error_data = response.json()
                    error = error_data.get('error', 'invalid_request')
                    error_description = error_data.get('error_description', 'Token exchange failed')
                    error_uri = error_data.get('error_uri')

                    print(f"❌ OAuth错误: {error}")
                    print(f"❌ 错误描述: {error_description}")

                    raise Exception(f"OAuth错误 ({error}): {error_description}")
                except json.JSONDecodeError:
                    raise Exception(f"令牌交换失败: HTTP {response.status_code} - {response.text}")

            else:
                raise Exception(f"令牌交换失败: HTTP {response.status_code} - {response.text}")

        except requests.exceptions.Timeout:
            raise Exception("令牌交换超时，请检查网络连接")
        except requests.exceptions.ConnectionError:
            raise Exception("无法连接到令牌服务器，请检查网络连接")
        except requests.exceptions.RequestException as e:
            raise Exception(f"令牌交换请求失败: {str(e)}")

    def refresh_token(self) -> Dict[str, Any]:
        """
        使用刷新令牌获取新的访问令牌

        Returns:
            Dict: 刷新结果
        """
        token_data = self.load_token()
        if not token_data or 'refresh_token' not in token_data:
            return {
                'success': False,
                'error': 'no_refresh_token',
                'error_description': '没有可用的刷新令牌'
            }

        refresh_request_data = {
            'grant_type': 'refresh_token',
            'client_id': self.CLIENT_ID,
            'refresh_token': token_data['refresh_token']
        }

        if self.CLIENT_SECRET:
            refresh_request_data['client_secret'] = self.CLIENT_SECRET

        headers = {
            'Content-Type': 'application/x-www-form-urlencoded',
            'Accept': 'application/json',
            'User-Agent': 'Windsurf-Desktop/1.0.0'
        }

        print("🔄 刷新访问令牌...")

        try:
            response = self.session.post(
                self.REFRESH_URL,
                data=refresh_request_data,
                headers=headers,
                timeout=30
            )

            if response.status_code == 200:
                new_token_data = response.json()
                new_token_data['obtained_at'] = int(time.time())

                # 如果没有新的刷新令牌，保留旧的
                if 'refresh_token' not in new_token_data and 'refresh_token' in token_data:
                    new_token_data['refresh_token'] = token_data['refresh_token']

                self._save_token(new_token_data)
                print("✅ 令牌刷新成功")

                return {'success': True, 'token_data': new_token_data}
            else:
                error_msg = f"令牌刷新失败: HTTP {response.status_code}"
                print(f"❌ {error_msg}")
                return {'success': False, 'error': 'refresh_failed', 'error_description': error_msg}

        except Exception as e:
            error_msg = f"令牌刷新异常: {str(e)}"
            print(f"❌ {error_msg}")
            return {'success': False, 'error': 'refresh_error', 'error_description': error_msg}

    def revoke_token(self) -> bool:
        """
        撤销访问令牌

        Returns:
            bool: 撤销是否成功
        """
        token_data = self.load_token()
        if not token_data:
            return True  # 没有令牌，视为成功

        access_token = token_data.get('access_token')
        refresh_token = token_data.get('refresh_token')

        success = True

        # 撤销访问令牌
        if access_token:
            success &= self._revoke_single_token(access_token, 'access_token')

        # 撤销刷新令牌
        if refresh_token:
            success &= self._revoke_single_token(refresh_token, 'refresh_token')

        # 删除本地令牌文件
        if os.path.exists(self.token_file):
            os.remove(self.token_file)
            print("🗑️ 本地令牌文件已删除")

        return success

    def _revoke_single_token(self, token: str, token_type: str) -> bool:
        """撤销单个令牌"""
        revoke_data = {
            'token': token,
            'token_type_hint': token_type,
            'client_id': self.CLIENT_ID
        }

        if self.CLIENT_SECRET:
            revoke_data['client_secret'] = self.CLIENT_SECRET

        try:
            response = self.session.post(
                self.REVOKE_URL,
                data=revoke_data,
                timeout=10
            )

            if response.status_code in [200, 204]:
                print(f"✅ {token_type} 撤销成功")
                return True
            else:
                print(f"⚠️ {token_type} 撤销失败: HTTP {response.status_code}")
                return False

        except Exception as e:
            print(f"❌ {token_type} 撤销异常: {e}")
            return False

    def _save_token(self, token_data: Dict[str, Any]):
        """保存令牌到本地文件"""
        try:
            with open(self.token_file, 'w', encoding='utf-8') as f:
                json.dump(token_data, f, indent=2, ensure_ascii=False)

            # 设置文件权限（仅所有者可读写）
            if os.name != 'nt':  # 非Windows系统
                os.chmod(self.token_file, 0o600)

            print(f"💾 令牌已保存到: {self.token_file}")

        except Exception as e:
            print(f"❌ 保存令牌失败: {e}")
            raise

    def _save_session(self, session_data: Dict[str, Any]):
        """保存会话数据"""
        try:
            with open(self.session_file, 'w', encoding='utf-8') as f:
                json.dump(session_data, f, indent=2, ensure_ascii=False)

            if os.name != 'nt':
                os.chmod(self.session_file, 0o600)

        except Exception as e:
            print(f"⚠️ 保存会话数据失败: {e}")

    def load_token(self) -> Optional[Dict[str, Any]]:
        """从本地文件加载令牌"""
        if not os.path.exists(self.token_file):
            return None

        try:
            with open(self.token_file, 'r', encoding='utf-8') as f:
                token_data = json.load(f)

            # 验证令牌数据完整性
            if not isinstance(token_data, dict) or 'access_token' not in token_data:
                print("⚠️ 令牌文件格式无效")
                return None

            return token_data

        except (json.JSONDecodeError, IOError) as e:
            print(f"❌ 加载令牌失败: {e}")
            return None

    def is_token_valid(self, token_data: Optional[Dict[str, Any]] = None) -> bool:
        """检查令牌是否有效"""
        if token_data is None:
            token_data = self.load_token()

        if not token_data or 'access_token' not in token_data:
            return False

        # 检查是否过期
        if 'expires_in' in token_data and 'obtained_at' in token_data:
            expires_at = token_data['obtained_at'] + token_data['expires_in']
            current_time = int(time.time())

            # 提前5分钟认为过期，留出刷新时间
            if current_time >= (expires_at - 300):
                print("⏰ 令牌即将过期或已过期")
                return False

        return True

    def get_valid_token(self) -> Optional[str]:
        """获取有效的访问令牌，自动刷新如果需要"""
        token_data = self.load_token()

        if not token_data:
            return None

        # 如果令牌有效，直接返回
        if self.is_token_valid(token_data):
            return token_data.get('access_token')

        # 尝试刷新令牌
        if 'refresh_token' in token_data:
            print("🔄 令牌已过期，尝试自动刷新...")
            refresh_result = self.refresh_token()

            if refresh_result.get('success'):
                new_token_data = refresh_result['token_data']
                return new_token_data.get('access_token')
            else:
                print("❌ 令牌刷新失败，需要重新登录")

        return None

    def get_user_info(self) -> Optional[Dict[str, Any]]:
        """获取用户信息"""
        token = self.get_valid_token()
        if not token:
            return None

        headers = {
            'Authorization': f'Bearer {token}',
            'Accept': 'application/json',
            'User-Agent': 'Windsurf-Desktop/1.0.0'
        }

        try:
            response = self.session.get(self.USER_INFO_URL, headers=headers, timeout=10)

            if response.status_code == 200:
                return response.json()
            elif response.status_code == 401:
                print("❌ 令牌无效，需要重新登录")
                return None
            else:
                print(f"⚠️ 获取用户信息失败: HTTP {response.status_code}")
                return None

        except Exception as e:
            print(f"❌ 获取用户信息异常: {e}")
            return None

    def test_api_connection(self) -> bool:
        """测试API连接"""
        token = self.get_valid_token()
        if not token:
            print("❌ 没有有效令牌")
            return False

        headers = {
            'Authorization': f'Bearer {token}',
            'Content-Type': 'application/json',
            'User-Agent': 'Windsurf-Desktop/1.0.0'
        }

        # 测试API端点
        test_endpoints = [
            f"{self.API_BASE_URL}/health",
            f"{self.API_BASE_URL}/api/v1/health",
            self.USER_INFO_URL
        ]

        for endpoint in test_endpoints:
            try:
                print(f"🧪 测试端点: {endpoint}")
                response = self.session.get(endpoint, headers=headers, timeout=5)

                if response.status_code in [200, 401]:  # 401表示认证端点存在但需要有效令牌
                    print(f"✅ 端点可达: {endpoint}")
                    return True

            except Exception as e:
                print(f"❌ 端点测试失败 {endpoint}: {e}")
                continue

        print("❌ 所有API端点测试失败")
        return False

    def logout(self):
        """完整登出流程"""
        print("🚪 开始登出...")

        # 撤销令牌
        if self.revoke_token():
            print("✅ 令牌撤销成功")
        else:
            print("⚠️ 令牌撤销失败，但将继续清理本地数据")

        # 清理本地文件
        files_to_remove = [self.token_file, self.session_file]
        for file_path in files_to_remove:
            if os.path.exists(file_path):
                try:
                    os.remove(file_path)
                    print(f"🗑️ 已删除: {file_path}")
                except Exception as e:
                    print(f"❌ 删除文件失败 {file_path}: {e}")

        print("✅ 登出完成")

    def get_token_info(self) -> Dict[str, Any]:
        """获取令牌详细信息"""
        token_data = self.load_token()
        if not token_data:
            return {'status': 'no_token', 'message': '没有保存的令牌'}

        current_time = int(time.time())
        obtained_at = token_data.get('obtained_at', 0)
        expires_in = token_data.get('expires_in', 0)
        expires_at = obtained_at + expires_in

        info = {
            'status': 'valid' if self.is_token_valid(token_data) else 'invalid',
            'token_type': token_data.get('token_type', 'Unknown'),
            'scope': token_data.get('scope', 'Unknown'),
            'obtained_at': obtained_at,
            'expires_in': expires_in,
            'expires_at': expires_at,
            'time_remaining': max(0, expires_at - current_time),
            'has_refresh_token': 'refresh_token' in token_data,
            'file_path': self.token_file
        }

        return info


def main():
    """主函数 - 完整的认证演示"""
    print("🚀 Windsurf 完整官方认证客户端")
    print("=" * 50)

    # 创建认证实例
    auth = WindsurfCompleteAuth()

    while True:
        print("\n请选择操作:")
        print("1. 登录")
        print("2. 查看令牌信息")
        print("3. 获取用户信息")
        print("4. 测试API连接")
        print("5. 刷新令牌")
        print("6. 登出")
        print("7. 退出")

        choice = input("\n请输入选项 (1-7): ").strip()

        if choice == '1':
            print("\n" + "=" * 30)
            result = auth.login()

            if result.get('success'):
                token = result.get('token', '')
                from_cache = result.get('from_cache', False)
                user_info = result.get('user_info', {})

                print(f"\n✅ 登录成功！")
                print(f"🔑 Token: {token[:30]}..." if token else "❌ 无Token")
                print(f"📊 来源: {'本地缓存' if from_cache else '新登录'}")

                if user_info:
                    print(f"👤 用户: {user_info.get('email', 'Unknown')}")
                    print(f"🆔 用户ID: {user_info.get('id', 'Unknown')}")
            else:
                print(f"\n❌ 登录失败: {result.get('error_description', 'Unknown error')}")

        elif choice == '2':
            print("\n" + "=" * 30)
            token_info = auth.get_token_info()

            print("📊 令牌信息:")
            print(f"   状态: {token_info.get('status', 'Unknown')}")
            print(f"   类型: {token_info.get('token_type', 'Unknown')}")
            print(f"   作用域: {token_info.get('scope', 'Unknown')}")
            print(f"   剩余时间: {token_info.get('time_remaining', 0)} 秒")
            print(f"   有刷新令牌: {'是' if token_info.get('has_refresh_token') else '否'}")
            print(f"   文件位置: {token_info.get('file_path', 'Unknown')}")

        elif choice == '3':
            print("\n" + "=" * 30)
            user_info = auth.get_user_info()

            if user_info:
                print("👤 用户信息:")
                for key, value in user_info.items():
                    print(f"   {key}: {value}")
            else:
                print("❌ 无法获取用户信息，请先登录")

        elif choice == '4':
            print("\n" + "=" * 30)
            if auth.test_api_connection():
                print("✅ API连接测试成功")
            else:
                print("❌ API连接测试失败")

        elif choice == '5':
            print("\n" + "=" * 30)
            result = auth.refresh_token()

            if result.get('success'):
                print("✅ 令牌刷新成功")
            else:
                print(f"❌ 令牌刷新失败: {result.get('error_description', 'Unknown error')}")

        elif choice == '6':
            print("\n" + "=" * 30)
            auth.logout()

        elif choice == '7':
            print("\n👋 再见！")
            break

        else:
            print("❌ 无效选项，请重试")


# 简化的使用接口
class WindsurfAuth:
    """简化的Windsurf认证接口"""

    def __init__(self, port: int = 8080):
        self._auth = WindsurfCompleteAuth(port=port)

    def login(self) -> str:
        """
        登录并返回访问令牌

        Returns:
            str: 访问令牌，失败返回None
        """
        result = self._auth.login()
        if result.get('success'):
            return result.get('token')
        return None

    def get_token(self) -> str:
        """
        获取有效的访问令牌

        Returns:
            str: 访问令牌，无效返回None
        """
        return self._auth.get_valid_token()

    def logout(self):
        """登出"""
        self._auth.logout()

    def is_logged_in(self) -> bool:
        """检查是否已登录"""
        return self._auth.get_valid_token() is not None


# 快速使用函数
def get_windsurf_token(port: int = 8080) -> str:
    """
    快速获取Windsurf访问令牌

    Args:
        port: 回调服务器端口

    Returns:
        str: 访问令牌，失败返回None
    """
    auth = WindsurfAuth(port=port)

    # 检查现有令牌
    existing_token = auth.get_token()
    if existing_token:
        print(f"✅ 使用现有令牌: {existing_token[:20]}...")
        return existing_token

    # 执行登录
    print("🔐 开始登录流程...")
    token = auth.login()

    if token:
        print(f"✅ 登录成功: {token[:20]}...")
        return token
    else:
        print("❌ 登录失败")
        return None


if __name__ == "__main__":
    main()
