#!/usr/bin/env python3
"""
Windsurf 完全自动认证模块
一键登录，自动获取token
"""

import os
import json
import time
import uuid
import webbrowser
import urllib.parse
from http.server import HTTPServer, BaseHTTPRequestHandler
from threading import Thread, Event
import requests
from typing import Optional, Dict, Any


class WindsurfFullAuto:
    """Windsurf完全自动认证客户端"""
    
    def __init__(self, data_dir: Optional[str] = None):
        """初始化认证客户端"""
        self.data_dir = data_dir or self._get_default_data_dir()
        self.token_file = os.path.join(self.data_dir, "auth_token.json")
        self.callback_server = None
        self.auth_result = {}
        self.server_ready = Event()
        os.makedirs(self.data_dir, exist_ok=True)
    
    def _get_default_data_dir(self) -> str:
        """获取默认的用户数据目录"""
        if os.name == 'nt':  # Windows
            return os.path.join(os.environ.get('APPDATA', ''), '.windsurf')
        elif os.uname().sysname == 'Darwin':  # macOS
            return os.path.expanduser('~/Library/Application Support/.windsurf')
        else:  # Linux
            return os.path.expanduser('~/.windsurf')
    
    def one_click_login(self) -> Dict[str, Any]:
        """一键登录 - 完全自动化"""
        print("🚀 Windsurf 一键登录开始...")
        
        # 生成状态参数
        state = str(uuid.uuid4())
        
        # 启动回调服务器
        print("🌐 启动回调服务器...")
        server_thread = Thread(target=self._start_callback_server, args=(state,), daemon=True)
        server_thread.start()
        
        if not self.server_ready.wait(timeout=5):
            return {'success': False, 'error': 'server_start_failed', 'error_description': '回调服务器启动失败'}
        
        print("✅ 回调服务器已启动")
        
        # 构建认证URL
        auth_url = f"http://localhost:8080/auth?state={state}"
        
        print("🔗 打开认证页面...")
        webbrowser.open(auth_url)
        
        # 等待认证完成
        print("⏳ 等待认证完成...")
        server_thread.join(timeout=300)  # 5分钟超时
        
        if not self.auth_result:
            return {'success': False, 'error': 'timeout', 'error_description': '认证超时'}
        
        # 如果认证成功，保存token
        if self.auth_result.get('success'):
            self._save_token(self.auth_result['token_data'])
            print("✅ 登录成功！Token已保存到本地。")
        else:
            print(f"❌ 登录失败: {self.auth_result.get('error_description', 'Unknown error')}")
        
        return self.auth_result
    
    def _start_callback_server(self, expected_state: str):
        """启动本地回调服务器"""
        class CallbackHandler(BaseHTTPRequestHandler):
            def __init__(self, auth_instance, expected_state, *args, **kwargs):
                self.auth_instance = auth_instance
                self.expected_state = expected_state
                super().__init__(*args, **kwargs)
            
            def log_message(self, format, *args):
                pass  # 禁用日志
            
            def do_GET(self):
                parsed_url = urllib.parse.urlparse(self.path)
                path = parsed_url.path
                query_params = urllib.parse.parse_qs(parsed_url.query)
                
                if path == '/auth':
                    # 显示认证页面
                    self._send_auth_page(query_params.get('state', [None])[0])
                elif path == '/callback':
                    # 处理认证回调
                    self._handle_callback(query_params)
                else:
                    self._send_404()
            
            def do_POST(self):
                if self.path == '/submit_token':
                    # 处理提交的token
                    content_length = int(self.headers['Content-Length'])
                    post_data = self.rfile.read(content_length).decode('utf-8')
                    params = urllib.parse.parse_qs(post_data)
                    
                    api_key = params.get('api_key', [None])[0]
                    if api_key and api_key.strip():
                        # 验证API密钥
                        result = self._validate_api_key(api_key.strip())
                        if result.get('success'):
                            self.auth_instance.auth_result = result
                            self._send_success_page()
                        else:
                            self._send_error_page(result.get('error_description', 'API密钥无效'))
                    else:
                        self._send_error_page('请输入有效的API密钥')
            
            def _validate_api_key(self, api_key: str) -> Dict[str, Any]:
                """验证API密钥"""
                try:
                    # 简单验证：检查格式
                    if len(api_key) < 20:
                        return {'success': False, 'error': 'invalid_format', 'error_description': 'API密钥格式无效'}
                    
                    # 创建token数据
                    token_data = {
                        'access_token': api_key,
                        'token_type': 'Bearer',
                        'saved_at': int(time.time()),
                        'expires_in': 86400 * 365  # 1年有效期
                    }
                    
                    return {'success': True, 'token_data': token_data}
                    
                except Exception as e:
                    return {'success': False, 'error': 'validation_error', 'error_description': str(e)}
            
            def _send_auth_page(self, state: str):
                """发送认证页面"""
                html = f"""<!DOCTYPE html>
                <html>
                <head>
                    <title>Windsurf 一键登录</title>
                    <meta charset="utf-8">
                    <style>
                        body {{ 
                            font-family: Arial, sans-serif; 
                            max-width: 600px; 
                            margin: 50px auto; 
                            padding: 20px;
                            background: #f5f5f5;
                        }}
                        .container {{ 
                            background: white; 
                            padding: 30px; 
                            border-radius: 10px; 
                            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                        }}
                        .logo {{ 
                            text-align: center; 
                            font-size: 24px; 
                            font-weight: bold; 
                            color: #007acc; 
                            margin-bottom: 30px;
                        }}
                        .step {{ 
                            margin: 20px 0; 
                            padding: 15px; 
                            background: #f8f9fa; 
                            border-left: 4px solid #007acc;
                        }}
                        input[type="text"] {{ 
                            width: 100%; 
                            padding: 12px; 
                            margin: 10px 0; 
                            border: 1px solid #ddd; 
                            border-radius: 5px;
                            font-size: 14px;
                        }}
                        button {{ 
                            background: #007acc; 
                            color: white; 
                            padding: 12px 30px; 
                            border: none; 
                            border-radius: 5px; 
                            cursor: pointer; 
                            font-size: 16px;
                            width: 100%;
                        }}
                        button:hover {{ background: #005a9e; }}
                        .link {{ color: #007acc; text-decoration: none; }}
                        .link:hover {{ text-decoration: underline; }}
                    </style>
                </head>
                <body>
                    <div class="container">
                        <div class="logo">🚀 Windsurf 一键登录</div>
                        
                        <div class="step">
                            <h3>📋 获取API密钥步骤：</h3>
                            <ol>
                                <li>点击 <a href="https://www.codeium.com/profile" target="_blank" class="link">Codeium Profile</a> 打开新标签页</li>
                                <li>登录您的Codeium账户</li>
                                <li>在"API Key"部分复制您的密钥</li>
                                <li>将密钥粘贴到下面的输入框中</li>
                            </ol>
                        </div>
                        
                        <form method="post" action="/submit_token">
                            <input type="text" name="api_key" placeholder="请粘贴您的Codeium API密钥" required>
                            <button type="submit">🔑 完成登录</button>
                        </form>
                        
                        <div style="text-align: center; margin-top: 20px; color: #666;">
                            <small>登录成功后，此页面将自动关闭</small>
                        </div>
                    </div>
                    
                    <script>
                        // 自动聚焦到输入框
                        document.querySelector('input[name="api_key"]').focus();
                        
                        // 监听粘贴事件
                        document.querySelector('input[name="api_key"]').addEventListener('paste', function(e) {{
                            setTimeout(() => {{
                                if (this.value.length > 20) {{
                                    document.querySelector('button').style.background = '#28a745';
                                    document.querySelector('button').textContent = '✅ 点击完成登录';
                                }}
                            }}, 100);
                        }});
                    </script>
                </body>
                </html>"""
                
                self.send_response(200)
                self.send_header('Content-type', 'text/html; charset=utf-8')
                self.end_headers()
                self.wfile.write(html.encode('utf-8'))
            
            def _send_success_page(self):
                """发送成功页面"""
                html = """<!DOCTYPE html>
                <html>
                <head>
                    <title>登录成功</title>
                    <meta charset="utf-8">
                    <style>
                        body { font-family: Arial, sans-serif; text-align: center; padding: 50px; background: #f5f5f5; }
                        .container { background: white; padding: 30px; border-radius: 10px; max-width: 400px; margin: 0 auto; }
                        .success { color: #28a745; font-size: 48px; margin-bottom: 20px; }
                    </style>
                </head>
                <body>
                    <div class="container">
                        <div class="success">✅</div>
                        <h1>登录成功！</h1>
                        <p>您的Windsurf认证已完成，现在可以关闭此页面。</p>
                    </div>
                    <script>
                        setTimeout(() => window.close(), 3000);
                    </script>
                </body>
                </html>"""
                
                self.send_response(200)
                self.send_header('Content-type', 'text/html; charset=utf-8')
                self.end_headers()
                self.wfile.write(html.encode('utf-8'))
            
            def _send_error_page(self, error_msg: str):
                """发送错误页面"""
                html = f"""<!DOCTYPE html>
                <html>
                <head>
                    <title>登录失败</title>
                    <meta charset="utf-8">
                    <style>
                        body {{ font-family: Arial, sans-serif; text-align: center; padding: 50px; background: #f5f5f5; }}
                        .container {{ background: white; padding: 30px; border-radius: 10px; max-width: 400px; margin: 0 auto; }}
                        .error {{ color: #dc3545; font-size: 48px; margin-bottom: 20px; }}
                    </style>
                </head>
                <body>
                    <div class="container">
                        <div class="error">❌</div>
                        <h1>登录失败</h1>
                        <p>{error_msg}</p>
                        <button onclick="history.back()">返回重试</button>
                    </div>
                </body>
                </html>"""
                
                self.send_response(400)
                self.send_header('Content-type', 'text/html; charset=utf-8')
                self.end_headers()
                self.wfile.write(html.encode('utf-8'))
            
            def _send_404(self):
                """发送404页面"""
                self.send_response(404)
                self.send_header('Content-type', 'text/html; charset=utf-8')
                self.end_headers()
                self.wfile.write(b'<h1>404 Not Found</h1>')
        
        # 创建服务器
        def handler(*args, **kwargs):
            return CallbackHandler(self, expected_state, *args, **kwargs)
        
        try:
            self.callback_server = HTTPServer(('localhost', 8080), handler)
            self.server_ready.set()
            print(f"🌐 认证服务器已启动，监听端口 8080")
            
            # 处理请求直到获得结果
            while not self.auth_result:
                self.callback_server.handle_request()
            
            self.callback_server.server_close()
            print("🔒 认证服务器已关闭")
        except Exception as e:
            print(f"❌ 认证服务器启动失败: {e}")
            self.auth_result = {'success': False, 'error': 'server_error', 'error_description': str(e)}
    
    def _save_token(self, token_data: Dict[str, Any]):
        """保存token到本地文件"""
        with open(self.token_file, 'w', encoding='utf-8') as f:
            json.dump(token_data, f, indent=2, ensure_ascii=False)
        if os.name != 'nt':  # 非Windows系统设置文件权限
            os.chmod(self.token_file, 0o600)
    
    def load_token(self) -> Optional[Dict[str, Any]]:
        """从本地文件加载token"""
        if not os.path.exists(self.token_file):
            return None
        try:
            with open(self.token_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except (json.JSONDecodeError, IOError):
            return None
    
    def is_token_valid(self, token_data: Optional[Dict[str, Any]] = None) -> bool:
        """检查token是否有效"""
        if token_data is None:
            token_data = self.load_token()
        if not token_data:
            return False
        # 检查是否过期
        if 'expires_in' in token_data and 'saved_at' in token_data:
            expires_at = token_data['saved_at'] + token_data['expires_in']
            if time.time() >= expires_at:
                return False
        return True
    
    def get_valid_token(self) -> Optional[str]:
        """获取有效的访问token"""
        token_data = self.load_token()
        if self.is_token_valid(token_data):
            return token_data.get('access_token')
        return None
    
    def logout(self):
        """登出，删除本地token"""
        if os.path.exists(self.token_file):
            os.remove(self.token_file)
            print("✅ 已登出，本地token已删除。")


def main():
    """主函数"""
    auth = WindsurfFullAuto()
    
    print("🚀 Windsurf 完全自动认证\n")
    
    # 检查现有token
    existing_token = auth.get_valid_token()
    if existing_token:
        print(f"✅ 发现有效token: {existing_token[:20]}...")
        print(f"📁 Token文件: {auth.token_file}")
        choice = input("是否重新登录？(y/N): ").strip().lower()
        if choice != 'y':
            return existing_token
    
    # 执行一键登录
    result = auth.one_click_login()
    
    if result.get('success'):
        token = auth.get_valid_token()
        print(f"\n🎉 一键登录成功！")
        print(f"🔑 Token: {token[:20] if token else 'None'}...")
        print(f"📁 Token文件: {auth.token_file}")
        return token
    else:
        print(f"\n💥 登录失败: {result.get('error_description')}")
        return None


if __name__ == "__main__":
    main()
