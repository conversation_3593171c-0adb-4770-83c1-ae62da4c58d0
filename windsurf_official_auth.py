#!/usr/bin/env python3
"""
Windsurf 官方认证实现
完全按照Windsurf官方的OAuth流程生成登录链接并自动获取token
"""

import os
import json
import time
import uuid
import hashlib
import base64
import secrets
import webbrowser
import urllib.parse
from http.server import HTTPServer, BaseHTTPRequestHandler
from threading import Thread, Event
import requests
from typing import Optional, Dict, Any


class WindsurfOfficialAuth:
    """Windsurf官方认证客户端 - 完全按照官方实现"""
    
    # 官方Windsurf/Codeium OAuth配置
    AUTH_BASE_URL = "https://www.codeium.com/profile"
    TOKEN_URL = "https://www.codeium.com/api/auth/oauth/token"
    API_BASE_URL = "https://server.codeium.com"
    
    # 官方OAuth客户端配置
    CLIENT_ID = "windsurf-desktop"
    REDIRECT_URI = "http://localhost:8080/callback"
    SCOPE = "openid profile email"
    
    def __init__(self, data_dir: Optional[str] = None):
        """初始化认证客户端"""
        self.data_dir = data_dir or self._get_default_data_dir()
        self.token_file = os.path.join(self.data_dir, "auth_token.json")
        self.callback_server = None
        self.auth_result = {}
        self.server_ready = Event()
        os.makedirs(self.data_dir, exist_ok=True)
    
    def _get_default_data_dir(self) -> str:
        """获取默认的用户数据目录"""
        if os.name == 'nt':  # Windows
            return os.path.join(os.environ.get('APPDATA', ''), '.windsurf')
        elif os.uname().sysname == 'Darwin':  # macOS
            return os.path.expanduser('~/Library/Application Support/.windsurf')
        else:  # Linux
            return os.path.expanduser('~/.windsurf')
    
    def _generate_pkce_pair(self) -> tuple[str, str]:
        """生成PKCE代码验证器和挑战 - 官方实现"""
        code_verifier = base64.urlsafe_b64encode(secrets.token_bytes(32)).decode('utf-8').rstrip('=')
        code_challenge = base64.urlsafe_b64encode(
            hashlib.sha256(code_verifier.encode('utf-8')).digest()
        ).decode('utf-8').rstrip('=')
        return code_verifier, code_challenge
    
    def generate_official_auth_url(self) -> tuple[str, str, str]:
        """生成官方OAuth认证URL - 与Windsurf完全一致"""
        state = str(uuid.uuid4())
        code_verifier, code_challenge = self._generate_pkce_pair()
        
        # 官方OAuth参数
        params = {
            'response_type': 'code',
            'client_id': self.CLIENT_ID,
            'redirect_uri': self.REDIRECT_URI,
            'scope': self.SCOPE,
            'state': state,
            'code_challenge': code_challenge,
            'code_challenge_method': 'S256',
        }
        
        auth_url = f"{self.AUTH_BASE_URL}?{urllib.parse.urlencode(params)}"
        return auth_url, state, code_verifier
    
    def auto_login(self) -> Dict[str, Any]:
        """自动登录 - 生成官方链接并自动获取token"""
        print("🚀 Windsurf官方认证开始...")
        
        # 检查现有token
        existing_token = self.get_valid_token()
        if existing_token:
            print(f"✅ 已有有效token: {existing_token[:20]}...")
            return {'success': True, 'token': existing_token, 'from_cache': True}
        
        # 生成官方认证URL
        auth_url, state, code_verifier = self.generate_official_auth_url()
        print(f"🔗 官方认证URL: {auth_url}")
        
        # 启动回调服务器
        print("🌐 启动回调服务器...")
        server_thread = Thread(target=self._start_callback_server, args=(state, code_verifier), daemon=True)
        server_thread.start()
        
        if not self.server_ready.wait(timeout=5):
            return {'success': False, 'error': 'server_start_failed', 'error_description': '回调服务器启动失败'}
        
        print("✅ 回调服务器已启动")
        
        # 自动打开浏览器
        print("🔗 自动打开浏览器...")
        try:
            webbrowser.open(auth_url)
            print("✅ 浏览器已打开，请完成登录")
        except Exception as e:
            print(f"❌ 浏览器打开失败: {e}")
            print(f"请手动访问: {auth_url}")
        
        # 等待认证完成
        print("⏳ 等待认证回调...")
        server_thread.join(timeout=300)  # 5分钟超时
        
        if not self.auth_result:
            return {'success': False, 'error': 'timeout', 'error_description': '认证超时'}
        
        # 如果认证成功，保存token
        if self.auth_result.get('success'):
            self._save_token(self.auth_result['token_data'])
            token = self.get_valid_token()
            print("✅ 登录成功！Token已保存到本地。")
            return {'success': True, 'token': token, 'from_cache': False}
        else:
            print(f"❌ 登录失败: {self.auth_result.get('error_description', 'Unknown error')}")
            return self.auth_result
    
    def _start_callback_server(self, expected_state: str, code_verifier: str):
        """启动本地回调服务器 - 处理OAuth回调"""
        class CallbackHandler(BaseHTTPRequestHandler):
            def __init__(self, auth_instance, expected_state, code_verifier, *args, **kwargs):
                self.auth_instance = auth_instance
                self.expected_state = expected_state
                self.code_verifier = code_verifier
                super().__init__(*args, **kwargs)
            
            def log_message(self, format, *args):
                pass  # 禁用日志
            
            def do_GET(self):
                # 解析回调URL
                parsed_url = urllib.parse.urlparse(self.path)
                query_params = urllib.parse.parse_qs(parsed_url.query)
                
                print(f"🔍 收到回调: {self.path}")
                
                # 检查错误
                if 'error' in query_params:
                    error = query_params['error'][0]
                    error_description = query_params.get('error_description', ['Unknown error'])[0]
                    self.auth_instance.auth_result = {
                        'success': False, 
                        'error': error, 
                        'error_description': error_description
                    }
                    self._send_response(f"❌ 登录失败: {error_description}")
                    return
                
                # 检查状态参数
                state = query_params.get('state', [None])[0]
                if state != self.expected_state:
                    self.auth_instance.auth_result = {
                        'success': False, 
                        'error': 'invalid_state', 
                        'error_description': '状态参数不匹配'
                    }
                    self._send_response("❌ 登录失败: 状态验证失败")
                    return
                
                # 获取授权码
                code = query_params.get('code', [None])[0]
                if not code:
                    self.auth_instance.auth_result = {
                        'success': False, 
                        'error': 'no_code', 
                        'error_description': '未收到授权码'
                    }
                    self._send_response("❌ 登录失败: 未收到授权码")
                    return
                
                print(f"🔑 收到授权码: {code[:20]}...")
                
                # 交换token
                try:
                    token_data = self.auth_instance._exchange_code_for_token(code, self.code_verifier)
                    self.auth_instance.auth_result = {'success': True, 'token_data': token_data}
                    self._send_response("✅ 登录成功！您现在可以关闭此页面。")
                    print("✅ Token交换成功")
                except Exception as e:
                    print(f"❌ Token交换失败: {e}")
                    self.auth_instance.auth_result = {
                        'success': False, 
                        'error': 'token_exchange_failed', 
                        'error_description': str(e)
                    }
                    self._send_response(f"❌ 登录失败: {str(e)}")
            
            def _send_response(self, message: str):
                """发送HTML响应"""
                html = f"""<!DOCTYPE html>
                <html>
                <head>
                    <title>Windsurf 认证</title>
                    <meta charset="utf-8">
                    <style>
                        body {{ 
                            font-family: Arial, sans-serif; 
                            text-align: center; 
                            padding: 50px; 
                            background: #f5f5f5;
                        }}
                        .container {{ 
                            background: white; 
                            padding: 30px; 
                            border-radius: 10px; 
                            max-width: 500px; 
                            margin: 0 auto;
                            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                        }}
                        .success {{ color: #28a745; }}
                        .error {{ color: #dc3545; }}
                    </style>
                </head>
                <body>
                    <div class="container">
                        <h1>🚀 Windsurf 认证</h1>
                        <p class="{'success' if '✅' in message else 'error'}">{message}</p>
                        <script>
                            setTimeout(() => window.close(), 3000);
                        </script>
                    </div>
                </body>
                </html>"""
                
                self.send_response(200)
                self.send_header('Content-type', 'text/html; charset=utf-8')
                self.end_headers()
                self.wfile.write(html.encode('utf-8'))
        
        # 创建服务器
        def handler(*args, **kwargs):
            return CallbackHandler(self, expected_state, code_verifier, *args, **kwargs)
        
        try:
            self.callback_server = HTTPServer(('localhost', 8080), handler)
            self.server_ready.set()
            print(f"🌐 回调服务器已启动，监听端口 8080")
            
            # 处理单个请求后关闭
            self.callback_server.handle_request()
            self.callback_server.server_close()
            print("🔒 回调服务器已关闭")
        except Exception as e:
            print(f"❌ 回调服务器启动失败: {e}")
            self.auth_result = {'success': False, 'error': 'server_error', 'error_description': str(e)}
    
    def _exchange_code_for_token(self, code: str, code_verifier: str) -> Dict[str, Any]:
        """使用授权码交换访问token - 官方实现"""
        token_data = {
            'grant_type': 'authorization_code',
            'client_id': self.CLIENT_ID,
            'code': code,
            'redirect_uri': self.REDIRECT_URI,
            'code_verifier': code_verifier,
        }
        
        headers = {
            'Content-Type': 'application/x-www-form-urlencoded',
            'User-Agent': 'Windsurf-Official-Client/1.0'
        }
        
        print(f"🔄 向 {self.TOKEN_URL} 交换token...")
        response = requests.post(self.TOKEN_URL, data=token_data, headers=headers, timeout=30)
        
        if response.status_code != 200:
            raise Exception(f"Token exchange failed: {response.status_code} - {response.text}")
        
        return response.json()
    
    def _save_token(self, token_data: Dict[str, Any]):
        """保存token到本地文件"""
        token_data['saved_at'] = int(time.time())
        with open(self.token_file, 'w', encoding='utf-8') as f:
            json.dump(token_data, f, indent=2, ensure_ascii=False)
        if os.name != 'nt':  # 非Windows系统设置文件权限
            os.chmod(self.token_file, 0o600)
    
    def load_token(self) -> Optional[Dict[str, Any]]:
        """从本地文件加载token"""
        if not os.path.exists(self.token_file):
            return None
        try:
            with open(self.token_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except (json.JSONDecodeError, IOError):
            return None
    
    def is_token_valid(self, token_data: Optional[Dict[str, Any]] = None) -> bool:
        """检查token是否有效"""
        if token_data is None:
            token_data = self.load_token()
        if not token_data:
            return False
        # 检查是否过期
        if 'expires_in' in token_data and 'saved_at' in token_data:
            expires_at = token_data['saved_at'] + token_data['expires_in']
            if time.time() >= expires_at:
                return False
        return True
    
    def get_valid_token(self) -> Optional[str]:
        """获取有效的访问token"""
        token_data = self.load_token()
        if self.is_token_valid(token_data):
            return token_data.get('access_token')
        return None
    
    def logout(self):
        """登出，删除本地token"""
        if os.path.exists(self.token_file):
            os.remove(self.token_file)
            print("✅ 已登出，本地token已删除。")


def main():
    """主函数"""
    auth = WindsurfOfficialAuth()
    
    print("🚀 Windsurf 官方认证客户端\n")
    
    # 执行自动登录
    result = auth.auto_login()
    
    if result.get('success'):
        token = result.get('token')
        from_cache = result.get('from_cache', False)
        
        print(f"\n🎉 认证成功！")
        print(f"🔑 Token: {token[:20] if token else 'None'}...")
        print(f"📁 Token文件: {auth.token_file}")
        print(f"📊 来源: {'本地缓存' if from_cache else '新登录'}")
        
        return token
    else:
        print(f"\n💥 认证失败: {result.get('error_description')}")
        return None


if __name__ == "__main__":
    main()
