#!/usr/bin/env python3
"""
Windsurf Playwright全自动认证模块
使用Playwright自动化浏览器完成登录
"""

import os
import json
import time
import uuid
import asyncio
from typing import Optional, Dict, Any


class WindsurfPlaywrightAuth:
    """Windsurf Playwright全自动认证客户端"""
    
    def __init__(self, data_dir: Optional[str] = None):
        """初始化认证客户端"""
        self.data_dir = data_dir or self._get_default_data_dir()
        self.token_file = os.path.join(self.data_dir, "auth_token.json")
        os.makedirs(self.data_dir, exist_ok=True)
    
    def _get_default_data_dir(self) -> str:
        """获取默认的用户数据目录"""
        if os.name == 'nt':  # Windows
            return os.path.join(os.environ.get('APPDATA', ''), '.windsurf')
        elif os.uname().sysname == 'Darwin':  # macOS
            return os.path.expanduser('~/Library/Application Support/.windsurf')
        else:  # Linux
            return os.path.expanduser('~/.windsurf')
    
    async def auto_login_with_credentials(self, email: str, password: str) -> Dict[str, Any]:
        """使用用户名密码全自动登录"""
        try:
            from playwright.async_api import async_playwright
        except ImportError:
            return {
                'success': False, 
                'error': 'playwright_not_installed', 
                'error_description': '请安装Playwright: pip install playwright && playwright install'
            }
        
        print("🚀 开始Playwright全自动登录...")
        
        async with async_playwright() as p:
            try:
                # 启动浏览器
                browser = await p.chromium.launch(headless=False)  # 设置为False以便调试
                context = await browser.new_context()
                page = await context.new_page()
                
                print("🌐 打开Codeium登录页面...")
                await page.goto("https://www.codeium.com/profile")
                
                # 等待页面加载
                await page.wait_for_load_state('networkidle')
                
                # 查找登录按钮或表单
                try:
                    # 尝试查找登录相关元素
                    login_button = await page.wait_for_selector('button:has-text("Log in"), a:has-text("Log in"), [data-testid="login"]', timeout=10000)
                    if login_button:
                        print("🔍 找到登录按钮，点击...")
                        await login_button.click()
                        await page.wait_for_load_state('networkidle')
                except:
                    print("🔍 未找到登录按钮，尝试直接查找登录表单...")
                
                # 查找邮箱输入框
                try:
                    email_input = await page.wait_for_selector('input[type="email"], input[name="email"], input[placeholder*="email"]', timeout=10000)
                    print("📧 找到邮箱输入框，输入邮箱...")
                    await email_input.fill(email)
                except:
                    print("❌ 未找到邮箱输入框")
                    await browser.close()
                    return {'success': False, 'error': 'email_input_not_found', 'error_description': '未找到邮箱输入框'}
                
                # 查找密码输入框
                try:
                    password_input = await page.wait_for_selector('input[type="password"], input[name="password"]', timeout=5000)
                    print("🔒 找到密码输入框，输入密码...")
                    await password_input.fill(password)
                except:
                    print("❌ 未找到密码输入框")
                    await browser.close()
                    return {'success': False, 'error': 'password_input_not_found', 'error_description': '未找到密码输入框'}
                
                # 查找提交按钮
                try:
                    submit_button = await page.wait_for_selector('button[type="submit"], button:has-text("Sign in"), button:has-text("Log in")', timeout=5000)
                    print("✅ 找到提交按钮，点击登录...")
                    await submit_button.click()
                except:
                    print("❌ 未找到提交按钮，尝试按回车...")
                    await page.keyboard.press('Enter')
                
                # 等待登录完成
                print("⏳ 等待登录完成...")
                await page.wait_for_load_state('networkidle')
                
                # 等待一段时间让页面完全加载
                await asyncio.sleep(3)
                
                # 尝试获取API密钥
                api_key = await self._extract_api_key(page)
                
                if api_key:
                    print("🔑 成功获取API密钥！")
                    token_data = {
                        'access_token': api_key,
                        'token_type': 'Bearer',
                        'saved_at': int(time.time()),
                        'expires_in': 86400 * 365  # 1年有效期
                    }
                    self._save_token(token_data)
                    await browser.close()
                    return {'success': True, 'token_data': token_data}
                else:
                    print("❌ 未能获取API密钥")
                    await browser.close()
                    return {'success': False, 'error': 'api_key_not_found', 'error_description': '登录成功但未能获取API密钥'}
                
            except Exception as e:
                print(f"❌ 浏览器操作失败: {e}")
                await browser.close()
                return {'success': False, 'error': 'browser_error', 'error_description': str(e)}
    
    async def _extract_api_key(self, page) -> Optional[str]:
        """从页面中提取API密钥"""
        try:
            # 尝试多种方法获取API密钥
            
            # 方法1: 查找包含API密钥的元素
            api_key_selectors = [
                '[data-testid="api-key"]',
                '.api-key',
                '#api-key',
                'code:has-text("codeium")',
                'input[readonly]',
                'textarea[readonly]'
            ]
            
            for selector in api_key_selectors:
                try:
                    element = await page.wait_for_selector(selector, timeout=2000)
                    if element:
                        text = await element.text_content()
                        if text and len(text) > 20 and 'codeium' in text.lower():
                            return text.strip()
                        
                        # 尝试获取value属性
                        value = await element.get_attribute('value')
                        if value and len(value) > 20:
                            return value.strip()
                except:
                    continue
            
            # 方法2: 查找页面中的所有文本，寻找API密钥模式
            page_content = await page.content()
            
            # 查找类似API密钥的字符串模式
            import re
            api_key_patterns = [
                r'[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}',  # UUID格式
                r'[A-Za-z0-9]{32,}',  # 长字符串
                r'codeium_[A-Za-z0-9_-]+',  # Codeium前缀
            ]
            
            for pattern in api_key_patterns:
                matches = re.findall(pattern, page_content)
                for match in matches:
                    if len(match) > 20:  # API密钥通常比较长
                        return match
            
            # 方法3: 尝试执行JavaScript获取
            try:
                api_key = await page.evaluate("""
                    () => {
                        // 查找所有可能包含API密钥的元素
                        const elements = document.querySelectorAll('input, textarea, code, pre, span');
                        for (let el of elements) {
                            const text = el.textContent || el.value || '';
                            if (text.length > 20 && (text.includes('codeium') || /^[a-f0-9-]{30,}$/.test(text))) {
                                return text.trim();
                            }
                        }
                        return null;
                    }
                """)
                if api_key:
                    return api_key
            except:
                pass
            
            return None
            
        except Exception as e:
            print(f"❌ 提取API密钥失败: {e}")
            return None
    
    def auto_login_interactive(self) -> Dict[str, Any]:
        """交互式全自动登录"""
        print("🚀 Windsurf Playwright全自动认证\n")
        
        email = input("请输入您的Codeium邮箱: ").strip()
        if not email:
            return {'success': False, 'error': 'no_email', 'error_description': '未输入邮箱'}
        
        import getpass
        password = getpass.getpass("请输入您的Codeium密码: ").strip()
        if not password:
            return {'success': False, 'error': 'no_password', 'error_description': '未输入密码'}
        
        # 运行异步登录
        return asyncio.run(self.auto_login_with_credentials(email, password))
    
    def _save_token(self, token_data: Dict[str, Any]):
        """保存token到本地文件"""
        with open(self.token_file, 'w', encoding='utf-8') as f:
            json.dump(token_data, f, indent=2, ensure_ascii=False)
        if os.name != 'nt':  # 非Windows系统设置文件权限
            os.chmod(self.token_file, 0o600)
    
    def load_token(self) -> Optional[Dict[str, Any]]:
        """从本地文件加载token"""
        if not os.path.exists(self.token_file):
            return None
        try:
            with open(self.token_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except (json.JSONDecodeError, IOError):
            return None
    
    def is_token_valid(self, token_data: Optional[Dict[str, Any]] = None) -> bool:
        """检查token是否有效"""
        if token_data is None:
            token_data = self.load_token()
        if not token_data:
            return False
        # 检查是否过期
        if 'expires_in' in token_data and 'saved_at' in token_data:
            expires_at = token_data['saved_at'] + token_data['expires_in']
            if time.time() >= expires_at:
                return False
        return True
    
    def get_valid_token(self) -> Optional[str]:
        """获取有效的访问token"""
        token_data = self.load_token()
        if self.is_token_valid(token_data):
            return token_data.get('access_token')
        return None
    
    def logout(self):
        """登出，删除本地token"""
        if os.path.exists(self.token_file):
            os.remove(self.token_file)
            print("✅ 已登出，本地token已删除。")


def main():
    """主函数"""
    auth = WindsurfPlaywrightAuth()
    
    # 检查现有token
    existing_token = auth.get_valid_token()
    if existing_token:
        print(f"✅ 发现有效token: {existing_token[:20]}...")
        print(f"📁 Token文件: {auth.token_file}")
        choice = input("是否重新登录？(y/N): ").strip().lower()
        if choice != 'y':
            return existing_token
    
    # 执行交互式全自动登录
    result = auth.auto_login_interactive()
    
    if result.get('success'):
        token = auth.get_valid_token()
        print(f"\n🎉 全自动登录成功！")
        print(f"🔑 Token: {token[:20] if token else 'None'}...")
        print(f"📁 Token文件: {auth.token_file}")
        return token
    else:
        print(f"\n💥 登录失败: {result.get('error_description')}")
        return None


if __name__ == "__main__":
    main()
