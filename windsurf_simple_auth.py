#!/usr/bin/env python3
"""
Windsurf 简化认证模块
基于Codeium API密钥的认证方式
"""

import os
import json
import time
import uuid
import webbrowser
import urllib.parse
from http.server import HTTPServer, BaseHTTPRequestHandler
from threading import Thread, Event
import requests
from typing import Optional, Dict, Any


class WindsurfSimpleAuth:
    """Windsurf简化认证客户端"""
    
    # Codeium API配置
    API_BASE_URL = "https://server.codeium.com"
    AUTH_URL = "https://www.codeium.com/profile?response_type=token&redirect_uri=http://localhost:8080/callback&state={state}"
    
    def __init__(self, data_dir: Optional[str] = None):
        """初始化认证客户端"""
        self.data_dir = data_dir or self._get_default_data_dir()
        self.token_file = os.path.join(self.data_dir, "auth_token.json")
        self.callback_server = None
        self.auth_result = {}
        self.server_ready = Event()
        os.makedirs(self.data_dir, exist_ok=True)
    
    def _get_default_data_dir(self) -> str:
        """获取默认的用户数据目录"""
        if os.name == 'nt':  # Windows
            return os.path.join(os.environ.get('APPDATA', ''), '.windsurf')
        elif os.uname().sysname == 'Darwin':  # macOS
            return os.path.expanduser('~/Library/Application Support/.windsurf')
        else:  # Linux
            return os.path.expanduser('~/.windsurf')
    
    def login_with_api_key(self, api_key: str) -> Dict[str, Any]:
        """使用API密钥登录"""
        try:
            # 验证API密钥
            headers = {
                'Authorization': f'Bearer {api_key}',
                'Content-Type': 'application/json'
            }
            
            # 测试API密钥是否有效
            response = requests.get(f"{self.API_BASE_URL}/exa.language_server_pb.LanguageServerService/GetCompletions", 
                                  headers=headers, timeout=10)
            
            if response.status_code == 200 or response.status_code == 400:  # 400可能是因为缺少请求体，但认证是成功的
                # 保存API密钥
                token_data = {
                    'access_token': api_key,
                    'token_type': 'Bearer',
                    'saved_at': int(time.time()),
                    'expires_in': 86400 * 365  # 1年有效期
                }
                self._save_token(token_data)
                return {'success': True, 'token_data': token_data}
            else:
                return {'success': False, 'error': 'invalid_api_key', 'error_description': 'API密钥无效'}
                
        except Exception as e:
            return {'success': False, 'error': 'api_error', 'error_description': str(e)}
    
    def login_browser(self) -> Dict[str, Any]:
        """通过浏览器登录获取token"""
        print("🚀 开始浏览器登录流程...")
        
        # 生成状态参数
        state = str(uuid.uuid4())
        auth_url = self.AUTH_URL.format(state=state)
        
        print(f"📝 生成认证URL: {auth_url}")
        
        # 启动回调服务器
        print("🌐 启动本地回调服务器...")
        server_thread = Thread(target=self._start_callback_server, args=(state,), daemon=True)
        server_thread.start()
        
        if not self.server_ready.wait(timeout=5):
            return {'success': False, 'error': 'server_start_failed', 'error_description': '回调服务器启动失败'}
        
        print("✅ 回调服务器已启动")
        
        # 打开浏览器
        print("🔗 打开浏览器进行认证...")
        print("📋 请在浏览器中登录Codeium，然后从URL中复制access_token参数")
        try:
            webbrowser.open(auth_url)
            print("✅ 浏览器已打开")
        except Exception as e:
            print(f"❌ 浏览器打开失败: {e}")
            print(f"请手动访问: {auth_url}")
        
        # 等待认证完成
        print("⏳ 等待用户完成认证...")
        server_thread.join(timeout=300)  # 5分钟超时
        
        if not self.auth_result:
            return {'success': False, 'error': 'timeout', 'error_description': '认证超时'}
        
        # 如果认证成功，保存token
        if self.auth_result.get('success'):
            self._save_token(self.auth_result['token_data'])
            print("✅ 登录成功！Token已保存到本地。")
        else:
            print(f"❌ 登录失败: {self.auth_result.get('error_description', 'Unknown error')}")
        
        return self.auth_result
    
    def _start_callback_server(self, expected_state: str):
        """启动本地回调服务器"""
        class CallbackHandler(BaseHTTPRequestHandler):
            def __init__(self, auth_instance, expected_state, *args, **kwargs):
                self.auth_instance = auth_instance
                self.expected_state = expected_state
                super().__init__(*args, **kwargs)
            
            def log_message(self, format, *args):
                pass  # 禁用日志
            
            def do_GET(self):
                # 解析回调URL
                parsed_url = urllib.parse.urlparse(self.path)
                
                # 检查fragment（#后面的部分）
                if '#' in self.path:
                    fragment = self.path.split('#')[1]
                    params = urllib.parse.parse_qs(fragment)
                else:
                    params = urllib.parse.parse_qs(parsed_url.query)
                
                print(f"🔍 收到回调: {self.path}")
                print(f"🔍 解析参数: {params}")
                
                # 检查access_token
                access_token = params.get('access_token', [None])[0]
                if access_token:
                    token_data = {
                        'access_token': access_token,
                        'token_type': 'Bearer',
                        'saved_at': int(time.time()),
                        'expires_in': 86400 * 365  # 1年有效期
                    }
                    self.auth_instance.auth_result = {'success': True, 'token_data': token_data}
                    self._send_response("登录成功！您现在可以关闭此页面。")
                else:
                    # 显示手动输入页面
                    self._send_input_page()
            
            def do_POST(self):
                # 处理手动输入的token
                content_length = int(self.headers['Content-Length'])
                post_data = self.rfile.read(content_length).decode('utf-8')
                params = urllib.parse.parse_qs(post_data)
                
                api_key = params.get('api_key', [None])[0]
                if api_key:
                    result = self.auth_instance.login_with_api_key(api_key.strip())
                    if result.get('success'):
                        self.auth_instance.auth_result = result
                        self._send_response("登录成功！您现在可以关闭此页面。")
                    else:
                        self._send_response(f"登录失败: {result.get('error_description')}")
                else:
                    self._send_response("请输入有效的API密钥")
            
            def _send_input_page(self):
                """发送手动输入页面"""
                html = """<!DOCTYPE html>
                <html>
                <head>
                    <title>Windsurf 认证</title>
                    <meta charset="utf-8">
                    <style>
                        body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
                        .container { max-width: 500px; margin: 0 auto; }
                        input[type="text"] { width: 100%; padding: 10px; margin: 10px 0; }
                        button { padding: 10px 20px; background: #007acc; color: white; border: none; cursor: pointer; }
                        .instructions { text-align: left; margin: 20px 0; padding: 15px; background: #f5f5f5; }
                    </style>
                </head>
                <body>
                    <div class="container">
                        <h1>Windsurf 认证</h1>
                        <div class="instructions">
                            <h3>获取API密钥步骤：</h3>
                            <ol>
                                <li>访问 <a href="https://www.codeium.com/profile" target="_blank">Codeium Profile</a></li>
                                <li>登录您的账户</li>
                                <li>在"API Key"部分复制您的密钥</li>
                                <li>将密钥粘贴到下面的输入框中</li>
                            </ol>
                        </div>
                        <form method="post">
                            <input type="text" name="api_key" placeholder="请输入您的Codeium API密钥" required>
                            <br>
                            <button type="submit">登录</button>
                        </form>
                    </div>
                </body>
                </html>"""
                
                self.send_response(200)
                self.send_header('Content-type', 'text/html; charset=utf-8')
                self.end_headers()
                self.wfile.write(html.encode('utf-8'))
            
            def _send_response(self, message: str):
                """发送响应页面"""
                html = f"""<!DOCTYPE html>
                <html>
                <head>
                    <title>Windsurf</title>
                    <meta charset="utf-8">
                    <style>body{{font-family:Arial,sans-serif;text-align:center;padding:50px}}</style>
                </head>
                <body><h1>Windsurf 认证</h1><p>{message}</p></body>
                </html>"""
                
                self.send_response(200)
                self.send_header('Content-type', 'text/html; charset=utf-8')
                self.end_headers()
                self.wfile.write(html.encode('utf-8'))
        
        # 创建服务器
        def handler(*args, **kwargs):
            return CallbackHandler(self, expected_state, *args, **kwargs)
        
        try:
            self.callback_server = HTTPServer(('localhost', 8080), handler)
            self.server_ready.set()
            print(f"🌐 回调服务器已启动，监听端口 8080")
            
            # 处理请求
            while True:
                self.callback_server.handle_request()
                if self.auth_result:  # 如果已经有结果，退出循环
                    break
            
            self.callback_server.server_close()
            print("🔒 回调服务器已关闭")
        except Exception as e:
            print(f"❌ 回调服务器启动失败: {e}")
            self.auth_result = {'success': False, 'error': 'server_error', 'error_description': str(e)}
    
    def _save_token(self, token_data: Dict[str, Any]):
        """保存token到本地文件"""
        with open(self.token_file, 'w', encoding='utf-8') as f:
            json.dump(token_data, f, indent=2, ensure_ascii=False)
        if os.name != 'nt':  # 非Windows系统设置文件权限
            os.chmod(self.token_file, 0o600)
    
    def load_token(self) -> Optional[Dict[str, Any]]:
        """从本地文件加载token"""
        if not os.path.exists(self.token_file):
            return None
        try:
            with open(self.token_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except (json.JSONDecodeError, IOError):
            return None
    
    def is_token_valid(self, token_data: Optional[Dict[str, Any]] = None) -> bool:
        """检查token是否有效"""
        if token_data is None:
            token_data = self.load_token()
        if not token_data:
            return False
        # 检查是否过期
        if 'expires_in' in token_data and 'saved_at' in token_data:
            expires_at = token_data['saved_at'] + token_data['expires_in']
            if time.time() >= expires_at:
                return False
        return True
    
    def get_valid_token(self) -> Optional[str]:
        """获取有效的访问token"""
        token_data = self.load_token()
        if self.is_token_valid(token_data):
            return token_data.get('access_token')
        return None
    
    def logout(self):
        """登出，删除本地token"""
        if os.path.exists(self.token_file):
            os.remove(self.token_file)


def main():
    """主函数"""
    auth = WindsurfSimpleAuth()
    
    print("🚀 Windsurf 简化认证\n")
    print("选择登录方式:")
    print("1. 使用API密钥登录（推荐）")
    print("2. 浏览器登录")
    
    choice = input("\n请选择 (1/2): ").strip()
    
    if choice == '1':
        api_key = input("请输入您的Codeium API密钥: ").strip()
        if api_key:
            result = auth.login_with_api_key(api_key)
            if result.get('success'):
                print("✅ 登录成功！")
                print(f"Token文件: {auth.token_file}")
            else:
                print(f"❌ 登录失败: {result.get('error_description')}")
    elif choice == '2':
        result = auth.login_browser()
        if result.get('success'):
            print("✅ 登录成功！")
            print(f"Token文件: {auth.token_file}")
        else:
            print(f"❌ 登录失败: {result.get('error_description')}")
    else:
        print("❌ 无效选择")


if __name__ == "__main__":
    main()
